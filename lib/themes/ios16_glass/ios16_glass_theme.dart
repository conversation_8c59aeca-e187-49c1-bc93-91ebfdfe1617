import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:glassmorphism/glassmorphism.dart';

/// iOS 16 Glassmorphism Theme cho Flutter
/// Tạo hiệu ứng kính mờ đẹp mắt như iOS 16
class iOS16GlassTheme {
  // === COLORS ===
  static const Color primaryGlass = Color(0x26FFFFFF);
  static const Color secondaryGlass = Color(0x1AFFFFFF);
  static const Color dialogGlass = Color(0xB3FFFFFF); // 70% opacity cho dialog
  static const Color backgroundGradientStart = Color(0xFF667eea);
  static const Color backgroundGradientEnd = Color(0xFF764ba2);
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xB3FFFFFF);
  static const Color accentColor = Color(0xFF007AFF);

  // === DIALOG GRADIENTS ===
  /// Gradient cho viền dialog
  static const LinearGradient dialogBorderGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFFFFFF), // Trắng đậm
      Color(0x80FFFFFF), // Trắng mờ 50%
      Color(0x40007AFF), // Accent color mờ
      Color(0x80FFFFFF), // Trắng mờ 50%
    ],
    stops: [0.0, 0.3, 0.7, 1.0],
  );

  // === DIALOG BACKGROUND COLORS ===
  /// Nền dialog cho light theme - trắng opacity 60%
  static const Color dialogBackgroundLight = Color(0x99FFFFFF); // 60% opacity

  /// Nền dialog cho dark theme - nâu đen opacity 50%
  static const Color dialogBackgroundDark = Color(0x80333333); // 50% opacity

  /// Lấy màu nền dialog dựa trên theme hệ thống
  static Color getDialogBackground(BuildContext context) {
    final brightness = MediaQuery.of(context).platformBrightness;
    return brightness == Brightness.dark
        ? dialogBackgroundDark
        : dialogBackgroundLight;
  }

  /// Lấy màu text title dựa trên theme hệ thống
  static Color getDialogTitleColor(BuildContext context) {
    final brightness = MediaQuery.of(context).platformBrightness;
    return brightness == Brightness.dark
        ? const Color(0xFFFFFFFF) // Trắng cho dark theme
        : const Color(0xFF000000); // Đen cho light theme
  }

  /// Lấy màu text content dựa trên theme hệ thống
  static Color getDialogContentColor(BuildContext context) {
    final brightness = MediaQuery.of(context).platformBrightness;
    return brightness == Brightness.dark
        ? const Color(0xFFCCCCCC) // Xám sáng cho dark theme
        : const Color(0xFF333333); // Xám đậm cho light theme
  }

  /// Gradient cho nền dialog dựa trên theme hệ thống
  static LinearGradient getDialogBackgroundGradient(BuildContext context) {
    final backgroundColor = getDialogBackground(context);
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        backgroundColor,
        backgroundColor.withValues(alpha: backgroundColor.a * 0.8),
      ],
    );
  }

  // === THEME DATA ===
  static ThemeData get theme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: accentColor,
      scaffoldBackgroundColor: Colors.transparent,
      fontFamily: 'SF Pro Display',

      // AppBar Theme
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: textPrimary,
          fontSize: 20,
          fontWeight: FontWeight.w600,
          fontFamily: 'SF Pro Display',
        ),
        iconTheme: IconThemeData(color: textPrimary),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
        ),
      ),

      // Text Theme
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          color: textPrimary,
          fontWeight: FontWeight.bold,
        ),
        displayMedium: TextStyle(
          color: textPrimary,
          fontWeight: FontWeight.bold,
        ),
        displaySmall: TextStyle(
          color: textPrimary,
          fontWeight: FontWeight.bold,
        ),
        headlineLarge: TextStyle(
          color: textPrimary,
          fontWeight: FontWeight.w600,
        ),
        headlineMedium: TextStyle(
          color: textPrimary,
          fontWeight: FontWeight.w600,
        ),
        headlineSmall: TextStyle(
          color: textPrimary,
          fontWeight: FontWeight.w600,
        ),
        titleLarge: TextStyle(color: textPrimary, fontWeight: FontWeight.w500),
        titleMedium: TextStyle(color: textPrimary, fontWeight: FontWeight.w500),
        titleSmall: TextStyle(color: textPrimary, fontWeight: FontWeight.w500),
        bodyLarge: TextStyle(color: textPrimary),
        bodyMedium: TextStyle(color: textPrimary),
        bodySmall: TextStyle(color: textSecondary),
        labelLarge: TextStyle(color: textPrimary, fontWeight: FontWeight.w500),
        labelMedium: TextStyle(color: textPrimary, fontWeight: FontWeight.w500),
        labelSmall: TextStyle(color: textSecondary),
      ),

      // Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryGlass,
          foregroundColor: textPrimary,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
      ),

      // Card Theme
      cardTheme: CardTheme(
        color: primaryGlass,
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      ),

      // Dialog Theme - sử dụng trong suốt vì chúng ta dùng iOS16GlassDialog
      dialogTheme: const DialogTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(20)),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: primaryGlass,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: accentColor, width: 2),
        ),
        labelStyle: const TextStyle(color: textSecondary),
        hintStyle: const TextStyle(color: textSecondary),
      ),

      // Color Scheme
      colorScheme: const ColorScheme.dark(
        primary: accentColor,
        secondary: accentColor,
        surface: primaryGlass,
        error: Color(0xFFFF453A),
        onPrimary: textPrimary,
        onSecondary: textPrimary,
        onSurface: textPrimary,
        onError: textPrimary,
        brightness: Brightness.dark,
      ),
    );
  }
}

/// Animated Background với gradient động
class iOS16AnimatedBackground extends StatefulWidget {
  final Widget child;

  const iOS16AnimatedBackground({super.key, required this.child});

  @override
  State<iOS16AnimatedBackground> createState() =>
      _iOS16AnimatedBackgroundState();
}

class _iOS16AnimatedBackgroundState extends State<iOS16AnimatedBackground>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(_controller);
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                iOS16GlassTheme.backgroundGradientStart,
                iOS16GlassTheme.backgroundGradientEnd,
                iOS16GlassTheme.backgroundGradientStart.withValues(alpha: 0.8),
              ],
              stops: [0.0, _animation.value, 1.0],
            ),
          ),
          child: widget.child,
        );
      },
    );
  }
}

/// Status Bar trong suốt cho iOS 16
class iOS16GlassStatusBar extends StatelessWidget {
  final Widget child;

  const iOS16GlassStatusBar({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
      child: child,
    );
  }
}

/// AppBar với hiệu ứng kính mờ
class iOS16GlassAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;

  const iOS16GlassAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
  });

  @override
  Widget build(BuildContext context) {
    return GlassmorphicContainer(
      width: double.infinity,
      height: preferredSize.height,
      borderRadius: 0,
      blur: 20,
      alignment: Alignment.bottomCenter,
      border: 0,
      linearGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [iOS16GlassTheme.primaryGlass, iOS16GlassTheme.secondaryGlass],
      ),
      borderGradient: const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Colors.transparent, Colors.transparent],
      ),
      child: AppBar(
        title: Text(title),
        actions: actions,
        leading: leading,
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// Container với hiệu ứng kính mờ
class iOS16GlassContainer extends StatelessWidget {
  final Widget child;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final double blur;
  final double opacity;

  const iOS16GlassContainer({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.borderRadius = 20.0,
    this.blur = 20.0,
    this.opacity = 0.1,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: GlassmorphicContainer(
        width: width ?? double.infinity,
        height: height ?? 100,
        borderRadius: borderRadius,
        blur: blur,
        alignment: Alignment.bottomCenter,
        border: 2,
        linearGradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withValues(alpha: opacity),
            Colors.white.withValues(alpha: opacity * 0.5),
          ],
        ),
        borderGradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withValues(alpha: 0.5),
            Colors.white.withValues(alpha: 0.2),
          ],
        ),
        child: Container(padding: padding, child: child),
      ),
    );
  }
}

/// Button với hiệu ứng kính mờ
class iOS16GlassButton extends StatefulWidget {
  final String text;
  final IconData? icon;
  final VoidCallback? onPressed;
  final double? width;
  final double? height;

  const iOS16GlassButton({
    super.key,
    required this.text,
    this.icon,
    this.onPressed,
    this.width,
    this.height,
  });

  @override
  State<iOS16GlassButton> createState() => _iOS16GlassButtonState();
}

class _iOS16GlassButtonState extends State<iOS16GlassButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) => _controller.reverse(),
      onTapCancel: () => _controller.reverse(),
      onTap: widget.onPressed,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: iOS16GlassContainer(
              width: widget.width,
              height: widget.height ?? 56,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (widget.icon != null) ...[
                    Icon(
                      widget.icon,
                      color: iOS16GlassTheme.textPrimary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                  ],
                  Text(
                    widget.text,
                    style: const TextStyle(
                      color: iOS16GlassTheme.textPrimary,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Card với hiệu ứng kính mờ và liquid animation
class iOS16GlassCard extends StatefulWidget {
  final Widget child;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final bool enableLiquidEffect;
  final VoidCallback? onTap;

  const iOS16GlassCard({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.enableLiquidEffect = true,
    this.onTap,
  });

  @override
  State<iOS16GlassCard> createState() => _iOS16GlassCardState();
}

class _iOS16GlassCardState extends State<iOS16GlassCard>
    with TickerProviderStateMixin {
  late AnimationController _liquidController;
  late AnimationController _tapController;
  late Animation<double> _liquidAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // Liquid animation controller
    _liquidController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    _liquidAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _liquidController, curve: Curves.easeInOut),
    );

    // Tap animation controller
    _tapController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(parent: _tapController, curve: Curves.easeInOut));

    if (widget.enableLiquidEffect) {
      _liquidController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _liquidController.dispose();
    _tapController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _tapController.forward(),
      onTapUp: (_) => _tapController.reverse(),
      onTapCancel: () => _tapController.reverse(),
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: Listenable.merge([_liquidAnimation, _scaleAnimation]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              margin: widget.margin,
              child: GlassmorphicContainer(
                width: widget.width ?? double.infinity,
                height: widget.height ?? 120,
                borderRadius: 20,
                blur: 15 + (_liquidAnimation.value * 5),
                alignment: Alignment.bottomCenter,
                border: 2,
                linearGradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withValues(
                      alpha: 0.15 + (_liquidAnimation.value * 0.05),
                    ),
                    Colors.white.withValues(
                      alpha: 0.05 + (_liquidAnimation.value * 0.03),
                    ),
                  ],
                ),
                borderGradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withValues(alpha: 0.6),
                    Colors.white.withValues(alpha: 0.2),
                  ],
                ),
                child: Container(
                  padding: widget.padding ?? const EdgeInsets.all(16),
                  child: widget.child,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Dialog với hiệu ứng kính mờ và viền gradient
class iOS16GlassDialog extends StatelessWidget {
  final Widget? title;
  final Widget? content;
  final List<Widget>? actions;
  final EdgeInsetsGeometry? contentPadding;
  final EdgeInsetsGeometry? titlePadding;
  final EdgeInsetsGeometry? actionsPadding;

  const iOS16GlassDialog({
    super.key,
    this.title,
    this.content,
    this.actions,
    this.contentPadding,
    this.titlePadding,
    this.actionsPadding,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: iOS16GlassTheme.dialogBorderGradient,
        ),
        padding: const EdgeInsets.all(3), // Độ dày viền gradient
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(17),
            gradient: iOS16GlassTheme.getDialogBackgroundGradient(context),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Title
              if (title != null)
                Padding(
                  padding:
                      titlePadding ?? const EdgeInsets.fromLTRB(24, 24, 24, 16),
                  child: DefaultTextStyle(
                    style: TextStyle(
                      color: iOS16GlassTheme.getDialogTitleColor(context),
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'SF Pro Display',
                    ),
                    child: title!,
                  ),
                ),

              // Content
              if (content != null)
                Flexible(
                  child: Padding(
                    padding:
                        contentPadding ??
                        const EdgeInsets.fromLTRB(24, 0, 24, 24),
                    child: DefaultTextStyle(
                      style: TextStyle(
                        color: iOS16GlassTheme.getDialogContentColor(context),
                        fontSize: 16,
                        fontFamily: 'SF Pro Display',
                      ),
                      child: content!,
                    ),
                  ),
                ),

              // Actions
              if (actions != null && actions!.isNotEmpty)
                Padding(
                  padding:
                      actionsPadding ??
                      const EdgeInsets.fromLTRB(24, 0, 24, 24),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children:
                        actions!
                            .map(
                              (action) => Padding(
                                padding: const EdgeInsets.only(left: 8),
                                child: action,
                              ),
                            )
                            .toList(),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Helper method để hiển thị iOS16GlassDialog
  static Future<T?> showGlassDialog<T>({
    required BuildContext context,
    Widget? title,
    Widget? content,
    List<Widget>? actions,
    bool barrierDismissible = true,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder:
          (context) => iOS16GlassDialog(
            title: title,
            content: content,
            actions: actions,
          ),
    );
  }

  /// Helper method để hiển thị dialog xác nhận với styling đẹp
  static Future<bool?> showConfirmDialog({
    required BuildContext context,
    required String title,
    required String content,
    String confirmText = 'Xác nhận',
    String cancelText = 'Hủy',
    Color? confirmColor,
    bool barrierDismissible = true,
  }) {
    return showGlassDialog<bool>(
      context: context,
      barrierDismissible: barrierDismissible,
      title: Text(title),
      content: Text(content),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          style: TextButton.styleFrom(
            foregroundColor: iOS16GlassTheme.getDialogContentColor(context),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          child: Text(cancelText),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(true),
          style: TextButton.styleFrom(
            foregroundColor: confirmColor ?? const Color(0xFFFF453A),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          child: Text(confirmText),
        ),
      ],
    );
  }
}
